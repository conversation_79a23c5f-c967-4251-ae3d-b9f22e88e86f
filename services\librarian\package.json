{"name": "@cktmcs/librarian", "version": "1.0.0", "description": "Librarian component for storing and retrieving data", "main": "dist/Librarian.js", "scripts": {"start": "ts-node src/Librarian.ts", "build": "tsc", "test": "jest"}, "dependencies": {"@cktmcs/errorhandler": "file:../../errorhandler", "@cktmcs/shared": "file:../../shared", "axios": "^1.12.0", "body-parser": "^1.19.0", "chromadb": "^1.10.5", "dotenv": "^16.5.0", "express": "^4.21.1", "mongodb": "^6.18.0", "redis": "^4.7.0"}, "devDependencies": {"@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/supertest": "^6.0.2", "jest": "^30.0.5", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-node": "^10.9.1", "typescript": "^5.6.3"}, "jest": {"preset": "ts-jest", "testEnvironment": "node"}}