/marketplace

GITHUB_TOKEN=
GITHUB_USERNAME=
GIT_REPOSITORY_URL=https://github.com/cpravetz/s7plugins.git
GIT_DEFAULT_BRANCH=main
GITHUB_EMAIL=

/services/agentset

CLIENT_SECRET=agentsetClientSecret

/services/brain

# OpenAI API
OPENAI_API_KEY=

# Gemini API
GEMINI_API_KEY=
GEMINI_API_URL=https://api.gemini.ai/generate


# HF
HUGGINGFACE_API_KEY=
HUGGINGFACE_API_URL=https://api-inference.huggingface.co/models/

# Anthropic API
ANTHROPIC_API_KEY=
ANTHROPIC_API_URL=https://api.anthropic.com/api/v1/

# OpenRouter API
OPENROUTER_API_KEY=

CLIENT_SECRET=brainClientSecret

OPENWEB_URL=
OPENWEBUI_API_KEY=

#GROQ_API_KEY=
GROQ_API_KEY=

MISTRAL_API_KEY=

CLOUDFLARE_WORKERS_AI_API_TOKEN=
CLOUDFLARE_WORKERS_AI_ACCOUNT_ID=

/services/capabilitiesmanager

CLIENT_SECRET=capabilitiesManagerClientSecret

GITHUB_TOKEN=
GITHUB_USERNAME=
GIT_REPOSITORY_URL=https://github.com/cpravetz/s7plugins.git
GIT_DEFAULT_BRANCH=main
GITHUB_EMAIL=
DEFAULT_PLUGIN_REPOSITORY=local
ENABLE_GITHUB=true
GOOGLE_SEARCH_API_KEY=
GOOGLE_CSE_ID=
LANGSEARCH_API_KEY=

/services/engineer

CLIENT_SECRET=engineerClientSecret

GITHUB_TOKEN=
GITHUB_USERNAME=
GIT_REPOSITORY_URL=https://github.com/cpravetz/s7plugins.git
GIT_DEFAULT_BRANCH=main
GITHUB_EMAIL=

/services/librarian

# Redis configuration
REDIS_HOST=127.0.0.1
REDIS_PORT=6379

# MongoDB configuration
MONGO_URI=mongodb://mongo:27017
MONGO_DB=librarianDB

CLIENT_SECRET=librarianClientSecret

/services/mcsreact

REACT_APP_API_BASE_URL=http://localhost:5020
REACT_APP_WS_URL=ws://localhost:5020
NODE_ENV=production
GENERATE_SOURCEMAP=false
SKIP_PREFLIGHT_CHECK=true

/services/missioncontrol

CLIENT_SECRET=missionControlClientSecret

/services/postoffice

CLIENT_SECRET=postOfficeClientSecret

/services/security

JWT_SECRET=
JWT_REFRESH_SECRET=
JWT_ACCESS_EXPIRATION=1h
JWT_REFRESH_EXPIRATION=7d
JWT_VERIFICATION_EXPIRATION=24h
JWT_RESET_EXPIRATION=1h


POSTOFFICE_URL='http://postoffice:5020'
PORT=5010

CLIENT_SECRET=securityClientSecret
CONFIG_SERVICE_SECRET=stage7AuthSecret
POSTOFFICE_SECRET=stage7AuthSecret
MISSIONCONTROL_SECRET=stage7AuthSecret
BRAIN_SECRET=stage7AuthSecret
LIBRARIAN_SECRET=stage7AuthSecret
ENGINEER_SECRET=stage7AuthSecret
TRAFFICMANAGER_SECRET=stage7AuthSecret
CAPABILITIESMANAGER_SECRET=stage7AuthSecret
AGENTSET_SECRET=stage7AuthSecret

# Database Configuration
LIBRARIAN_URL=librarian:5040


# Email Configuration
EMAIL_HOST=
EMAIL_PORT=
EMAIL_SECURE=
EMAIL_USER=
EMAIL_PASS=
EMAIL_FROM=


# Frontend URL (for email links)
FRONTEND_URL=http://localhost:3000

# Account Security
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=30 # minutes

/services/trafficmanager

CLIENT_SECRET=trafficManagerClientSecret