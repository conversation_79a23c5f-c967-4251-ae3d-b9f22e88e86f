{"id": "plugin-API_CLIENT", "verb": "API_CLIENT", "description": "A generic interface for interacting with third-party RESTful APIs. Use this plugin only when you have a complete and valid URL, method, and any required authentication details. Do not use this for general web browsing or if you are unsure about the API's specifics.", "explanation": "This plugin provides a command to make HTTP requests to any RESTful API, handling various authentication methods and returning the full HTTP response. You are responsible for providing any keys, IDs or required request parameters needed to use the API. This plugin will fail if the endpoint is invalid or the request is not properly formatted, but the failure will not stop the parent agent.", "inputDefinitions": [{"name": "method", "required": true, "type": "string", "description": "The HTTP method (e.g., GET, POST, PUT, DELETE).", "aliases": ["httpMethod", "verb", "http_method"]}, {"name": "url", "required": true, "type": "string", "description": "The API endpoint URL. This must be a full, valid, and accessible URL.", "aliases": ["endpoint", "uri"]}, {"name": "headers", "required": false, "type": "object", "description": "A dictionary of HTTP headers.", "aliases": ["http_headers", "hdrs", "header"]}, {"name": "body", "required": false, "type": "object", "description": "The request body for methods like POST or PUT.", "aliases": ["payload", "data"]}, {"name": "auth", "required": false, "type": "object", "description": "Authentication details (e.g., API key, bearer token).", "aliases": ["authentication", "credentials", "auth_info"]}], "inputGuidance": "Required inputs include 'method' and 'url'. The URL must be a complete and valid API endpoint. This tool is not for general web browsing. If you do not have a specific API endpoint to call, use a different tool. The user is responsible for providing all necessary information to make a valid API call.", "outputDefinitions": [{"name": "status_code", "required": true, "type": "number", "description": "The HTTP status code of the response."}, {"name": "headers", "required": true, "type": "object", "description": "The response headers."}, {"name": "body", "required": true, "type": "object", "description": "The response body."}], "language": "python", "entryPoint": {"main": "main.py", "function": "execute_plugin"}, "repository": {"type": "local"}, "security": {"permissions": ["net.fetch"]}, "version": "1.0.0", "metadata": {"author": "Stage7 Development Team", "tags": ["api", "rest", "http", "client"], "category": "utility", "license": "MIT"}}